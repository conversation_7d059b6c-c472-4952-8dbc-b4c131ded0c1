"use client"

import { useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import {
  Home,
  Search,
  Compass,
  Bell,
  User,
  ChevronLeft,
  MoreVertical,
  Heart,
  MessageCircle,
  Bookmark,
  Share2,
  Zap,
  Sparkles,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Avatar } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import ThemeToggle from "./theme-toggle"

const screens = ["home", "discover", "notifications", "profile"]

export default function MobileApp() {
  const [activeScreen, setActiveScreen] = useState("home")
  const [showDetails, setShowDetails] = useState(false)

  return (
    <div className="w-full max-w-sm mx-auto">
      <div className="rounded-3xl overflow-hidden border border-slate-800 bg-black shadow-xl shadow-violet-500/10">
        {/* Status Bar */}
        <div className="flex items-center justify-between px-6 py-2 bg-slate-950">
          <div className="text-xs font-medium text-slate-300">9:41</div>
          <div className="flex items-center space-x-1">
            <div className="text-xs font-medium text-slate-300">5G</div>
            <div className="w-4 h-4 flex items-center justify-center">
              <svg viewBox="0 0 24 24" className="w-3 h-3 text-slate-300">
                <path
                  fill="currentColor"
                  d="M12,21L15.6,16.2C14.6,15.45 13.35,15 12,15C10.65,15 9.4,15.45 8.4,16.2L12,21M12,3C7.95,3 4.21,4.34 1.2,6.6L3,9C5.5,7.12 8.62,6 12,6C15.38,6 18.5,7.12 21,9L22.8,6.6C19.79,4.34 16.05,3 12,3M12,9C9.3,9 6.81,9.89 4.8,11.4L6.6,13.8C8.1,12.67 9.97,12 12,12C14.03,12 15.9,12.67 17.4,13.8L19.2,11.4C17.19,9.89 14.7,9 12,9Z"
                />
              </svg>
            </div>
            <div className="w-4 h-4 flex items-center justify-center">
              <svg viewBox="0 0 24 24" className="w-3 h-3 text-slate-300">
                <path
                  fill="currentColor"
                  d="M19.77,7.23L19.78,7.22L16.06,3.5L15,4.56L17.11,6.67C16.17,7.03 15.5,7.93 15.5,9A2.5,2.5 0 0,0 18,11.5C19.07,11.5 20,10.83 20.36,9.89L21.42,11L22.5,9.92L19.77,7.23M18,9.5A1,1 0 0,1 17,8.5A1,1 0 0,1 18,7.5A1,1 0 0,1 19,8.5A1,1 0 0,1 18,9.5M17,14H19V17H22V19H19V22H17V19H14V17H17V14M5,9.5A2.5,2.5 0 0,0 7.5,12A2.5,2.5 0 0,0 10,9.5C10,8.12 8.88,7 7.5,7A2.5,2.5 0 0,0 5,9.5M7.5,8.5A1,1 0 0,1 8.5,9.5A1,1 0 0,1 7.5,10.5A1,1 0 0,1 6.5,9.5A1,1 0 0,1 7.5,8.5M3,14H6V17H9V19H6V22H3V19H0V17H3V14Z"
                />
              </svg>
            </div>
            <div className="w-5 h-4 flex items-center justify-center">
              <div className="w-4 h-2.5 bg-slate-300 rounded-sm relative overflow-hidden">
                <div className="absolute inset-0 bg-slate-950 right-1"></div>
              </div>
            </div>
          </div>
        </div>

        {/* App Content */}
        <div className="h-[600px] relative bg-gradient-to-br from-slate-950 to-slate-900">
          {/* Header */}
          <header className="flex items-center justify-between p-4 backdrop-blur-md bg-black/40 border-b border-slate-800/50">
            {activeScreen !== "home" ? (
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full text-slate-300 hover:text-white hover:bg-slate-800"
                onClick={() => setActiveScreen("home")}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            ) : (
              <div className="flex items-center">
                <Sparkles className="h-5 w-5 mr-2 text-violet-400" />
                <h1 className="text-lg font-bold bg-gradient-to-r from-violet-400 to-fuchsia-400 bg-clip-text text-transparent">
                  Nebula
                </h1>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full text-slate-300 hover:text-white hover:bg-slate-800"
              >
                <MoreVertical className="h-5 w-5" />
              </Button>
            </div>
          </header>

          {/* Main Content Area */}
          <ScrollArea className="h-[calc(600px-128px)]">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeScreen}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="p-4"
              >
                {activeScreen === "home" && <HomeScreen setShowDetails={setShowDetails} />}
                {activeScreen === "discover" && <DiscoverScreen />}
                {activeScreen === "notifications" && <NotificationsScreen />}
                {activeScreen === "profile" && <ProfileScreen />}
              </motion.div>
            </AnimatePresence>
          </ScrollArea>

          {/* Detail Modal */}
          <AnimatePresence>
            {showDetails && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ type: "spring", damping: 25, stiffness: 300 }}
                className="absolute inset-0 bg-black/80 backdrop-blur-md z-10 flex flex-col"
              >
                <div className="flex items-center justify-between p-4 border-b border-slate-800/50">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full text-slate-300 hover:text-white hover:bg-slate-800"
                    onClick={() => setShowDetails(false)}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <h2 className="text-lg font-semibold text-white">Details</h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full text-slate-300 hover:text-white hover:bg-slate-800"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </Button>
                </div>
                <div className="flex-1 p-4 overflow-auto">
                  <div className="aspect-square w-full rounded-2xl overflow-hidden mb-4 bg-gradient-to-br from-violet-500/20 to-fuchsia-500/20 border border-slate-700/50">
                    <img
                      src="/placeholder.svg?height=400&width=400"
                      alt="Featured content"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Avatar className="border-2 border-violet-500 h-8 w-8">
                        <img src="/placeholder.svg?height=32&width=32" alt="User" />
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium text-white">Alex Morgan</p>
                        <p className="text-xs text-slate-400">2 hours ago</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      className="rounded-full bg-gradient-to-r from-violet-600 to-fuchsia-600 hover:from-violet-500 hover:to-fuchsia-500 text-white border-0"
                    >
                      Follow
                    </Button>
                  </div>
                  <p className="text-slate-300 mb-6">
                    Exploring new design techniques for our upcoming project. The combination of glassmorphism and
                    dynamic color gradients creates an immersive experience that adapts to user interaction.
                  </p>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-slate-300 space-x-1 hover:text-white hover:bg-slate-800"
                      >
                        <Heart className="h-4 w-4" />
                        <span>2.4k</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-slate-300 space-x-1 hover:text-white hover:bg-slate-800"
                      >
                        <MessageCircle className="h-4 w-4" />
                        <span>482</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-slate-300 space-x-1 hover:text-white hover:bg-slate-800"
                      >
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white hover:bg-slate-800">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-white">Comments</h3>
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex space-x-2">
                        <Avatar className="h-6 w-6">
                          <img src={`/placeholder.svg?height=24&width=24&text=U${i}`} alt="User" />
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <p className="text-xs font-medium text-white">User{i}</p>
                            <p className="text-xs text-slate-400">{i * 10}m ago</p>
                          </div>
                          <p className="text-xs text-slate-300">
                            Amazing work! The color palette is stunning and the animations are so smooth.
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Bar */}
          <div className="absolute bottom-0 left-0 right-0 backdrop-blur-md bg-black/40 border-t border-slate-800/50">
            <div className="flex items-center justify-around p-4">
              {[
                { name: "home", icon: Home },
                { name: "discover", icon: Compass },
                { name: "notifications", icon: Bell },
                { name: "profile", icon: User },
              ].map((item) => (
                <Button
                  key={item.name}
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "rounded-full relative",
                    activeScreen === item.name
                      ? "text-white bg-gradient-to-r from-violet-600/20 to-fuchsia-600/20"
                      : "text-slate-500 hover:text-slate-300 hover:bg-slate-800/50",
                  )}
                  onClick={() => setActiveScreen(item.name)}
                >
                  <item.icon className="h-5 w-5" />
                  {item.name === "notifications" && (
                    <span className="absolute top-0 right-0 w-2 h-2 bg-rose-500 rounded-full" />
                  )}
                  {activeScreen === item.name && (
                    <motion.div
                      layoutId="nav-indicator"
                      className="absolute -bottom-1 left-1/2 w-1 h-1 bg-violet-500 rounded-full"
                      initial={{ x: "-50%" }}
                      animate={{ x: "-50%" }}
                      transition={{ type: "spring", damping: 15, stiffness: 300 }}
                    />
                  )}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function HomeScreen({ setShowDetails }: { setShowDetails: (show: boolean) => void }) {
  return (
    <div className="space-y-6">
      {/* Stories */}
      <div className="pb-2">
        <h2 className="text-sm font-medium text-slate-400 mb-3">Stories</h2>
        <div className="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex flex-col items-center space-y-1">
              <div
                className={cn(
                  "w-16 h-16 rounded-full p-0.5",
                  i === 1 ? "bg-gradient-to-r from-violet-500 to-fuchsia-500" : "bg-slate-700",
                )}
              >
                <div className="bg-black rounded-full p-0.5 h-full w-full">
                  <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-full h-full w-full overflow-hidden">
                    <img
                      src={`/placeholder.svg?height=64&width=64&text=U${i}`}
                      alt={`User ${i}`}
                      className="h-full w-full object-cover"
                    />
                  </div>
                </div>
              </div>
              <span className="text-xs text-slate-400">User {i}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Featured Card */}
      <div
        className="rounded-2xl overflow-hidden bg-gradient-to-br from-slate-900 to-slate-950 border border-slate-800/50 shadow-lg shadow-violet-500/5"
        onClick={() => setShowDetails(true)}
      >
        <div className="aspect-video w-full bg-gradient-to-br from-violet-500/20 to-fuchsia-500/20">
          <img
            src="/placeholder.svg?height=300&width=500"
            alt="Featured content"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Avatar className="border-2 border-violet-500 h-8 w-8">
                <img src="/placeholder.svg?height=32&width=32" alt="User" />
              </Avatar>
              <div>
                <p className="text-sm font-medium text-white">Alex Morgan</p>
                <p className="text-xs text-slate-400">2 hours ago</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full text-slate-400 hover:text-white hover:bg-slate-800"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-slate-300 mb-3 line-clamp-2">
            Exploring new design techniques for our upcoming project. The combination of glassmorphism and dynamic color
            gradients creates an immersive experience.
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-400 space-x-1 hover:text-white hover:bg-slate-800"
              >
                <Heart className="h-4 w-4" />
                <span>2.4k</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-400 space-x-1 hover:text-white hover:bg-slate-800"
              >
                <MessageCircle className="h-4 w-4" />
                <span>482</span>
              </Button>
            </div>
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white hover:bg-slate-800">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Feed Cards */}
      <div className="space-y-4">
        {[1, 2].map((i) => (
          <div
            key={i}
            className="rounded-xl overflow-hidden bg-gradient-to-br from-slate-900 to-slate-950 border border-slate-800/50 shadow-lg shadow-violet-500/5"
            onClick={() => setShowDetails(true)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <img src={`/placeholder.svg?height=32&width=32&text=U${i + 1}`} alt="User" />
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium text-white">User {i + 1}</p>
                    <p className="text-xs text-slate-400">{i} hour ago</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full text-slate-400 hover:text-white hover:bg-slate-800"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-slate-300 mb-3">
                Just discovered this amazing new design trend for 2025! What do you think?
              </p>
              <div className="rounded-lg overflow-hidden bg-gradient-to-br from-slate-800/50 to-slate-900/50 mb-3">
                <img
                  src={`/placeholder.svg?height=200&width=400&text=Post${i}`}
                  alt="Post content"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-slate-400 space-x-1 hover:text-white hover:bg-slate-800"
                  >
                    <Heart className="h-4 w-4" />
                    <span>{i * 1.2}k</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-slate-400 space-x-1 hover:text-white hover:bg-slate-800"
                  >
                    <MessageCircle className="h-4 w-4" />
                    <span>{i * 120}</span>
                  </Button>
                </div>
                <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white hover:bg-slate-800">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function DiscoverScreen() {
  return (
    <div className="space-y-6">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search className="h-4 w-4 text-slate-400" />
        </div>
        <input
          type="search"
          className="block w-full p-2.5 pl-10 text-sm rounded-lg bg-slate-800/50 border border-slate-700 placeholder-slate-400 text-white focus:ring-violet-500 focus:border-violet-500"
          placeholder="Search for inspiration..."
        />
      </div>

      <div>
        <h2 className="text-sm font-medium text-slate-400 mb-3">Trending Topics</h2>
        <div className="flex flex-wrap gap-2">
          {["Design", "UI/UX", "Animation", "Glassmorphism", "Dark Mode", "Gradients", "3D", "Typography"].map(
            (tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="bg-slate-800/50 text-slate-300 hover:bg-violet-500/20 hover:text-violet-300 transition-colors border-slate-700 hover:border-violet-500/50"
              >
                {tag}
              </Badge>
            ),
          )}
        </div>
      </div>

      <div>
        <h2 className="text-sm font-medium text-slate-400 mb-3">Discover</h2>
        <div className="grid grid-cols-2 gap-3">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="rounded-xl overflow-hidden bg-gradient-to-br from-slate-900 to-slate-950 border border-slate-800/50 shadow-lg shadow-violet-500/5"
            >
              <div className="aspect-square w-full bg-gradient-to-br from-violet-500/10 to-fuchsia-500/10">
                <img
                  src={`/placeholder.svg?height=200&width=200&text=D${i}`}
                  alt="Discover content"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Avatar className="h-5 w-5">
                      <img src={`/placeholder.svg?height=20&width=20&text=U${i}`} alt="User" />
                    </Avatar>
                    <p className="text-xs font-medium text-white truncate max-w-[60px]">Creator{i}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-slate-400 hover:text-white hover:bg-slate-800"
                  >
                    <Heart className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-sm font-medium text-slate-400 mb-3">Popular Creators</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-br from-slate-900 to-slate-950 border border-slate-800/50"
            >
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10 border border-slate-700">
                  <img src={`/placeholder.svg?height=40&width=40&text=C${i}`} alt="Creator" />
                </Avatar>
                <div>
                  <p className="text-sm font-medium text-white">Creator {i}</p>
                  <p className="text-xs text-slate-400">{i * 10}k followers</p>
                </div>
              </div>
              <Button
                size="sm"
                variant={i === 1 ? "default" : "outline"}
                className={
                  i === 1
                    ? "rounded-full bg-gradient-to-r from-violet-600 to-fuchsia-600 hover:from-violet-500 hover:to-fuchsia-500 text-white border-0"
                    : "rounded-full border-slate-700 text-slate-300 hover:text-white hover:bg-slate-800"
                }
              >
                {i === 1 ? "Following" : "Follow"}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function NotificationsScreen() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-sm font-medium text-slate-400">Notifications</h2>
        <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white hover:bg-slate-800">
          Mark all as read
        </Button>
      </div>

      <div className="space-y-1">
        <h3 className="text-xs font-medium text-slate-500 uppercase tracking-wider">New</h3>
        <div className="space-y-2">
          {[1, 2].map((i) => (
            <div
              key={i}
              className="flex items-start space-x-3 p-3 rounded-xl bg-gradient-to-br from-violet-500/10 to-fuchsia-500/10 border border-slate-800/50"
            >
              <Avatar className="h-8 w-8">
                <img src={`/placeholder.svg?height=32&width=32&text=U${i}`} alt="User" />
              </Avatar>
              <div className="flex-1">
                <p className="text-sm text-slate-300">
                  <span className="font-medium text-white">User {i}</span>{" "}
                  {i === 1 ? "liked your post" : "commented on your design"}
                </p>
                <p className="text-xs text-slate-400">Just now</p>
              </div>
              <div className="h-2 w-2 rounded-full bg-violet-500"></div>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-1">
        <h3 className="text-xs font-medium text-slate-500 uppercase tracking-wider">Earlier</h3>
        <div className="space-y-2">
          {[3, 4, 5].map((i) => (
            <div
              key={i}
              className="flex items-start space-x-3 p-3 rounded-xl bg-gradient-to-br from-slate-900 to-slate-950 border border-slate-800/50"
            >
              <Avatar className="h-8 w-8">
                <img src={`/placeholder.svg?height=32&width=32&text=U${i}`} alt="User" />
              </Avatar>
              <div className="flex-1">
                <p className="text-sm text-slate-300">
                  <span className="font-medium text-white">User {i}</span>{" "}
                  {i === 3 ? "started following you" : i === 4 ? "mentioned you in a comment" : "shared your post"}
                </p>
                <p className="text-xs text-slate-400">
                  {i - 2} hour{i - 2 > 1 ? "s" : ""} ago
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function ProfileScreen() {
  return (
    <div className="space-y-6">
      <div className="relative">
        <div className="h-24 rounded-xl bg-gradient-to-r from-violet-600/20 to-fuchsia-600/20 overflow-hidden">
          <img
            src="/placeholder.svg?height=100&width=400&text=Cover"
            alt="Cover"
            className="w-full h-full object-cover opacity-50"
          />
        </div>
        <div className="absolute -bottom-10 left-4 border-4 border-slate-950 rounded-full">
          <Avatar className="h-20 w-20">
            <img src="/placeholder.svg?height=80&width=80&text=Me" alt="Profile" />
          </Avatar>
        </div>
      </div>

      <div className="pt-10 flex items-end justify-between">
        <div>
          <h2 className="text-lg font-bold text-white">Alex Morgan</h2>
          <p className="text-sm text-slate-400">@alexmorgan</p>
        </div>
        <Button
          size="sm"
          className="rounded-full bg-gradient-to-r from-violet-600 to-fuchsia-600 hover:from-violet-500 hover:to-fuchsia-500 text-white border-0"
        >
          Edit Profile
        </Button>
      </div>

      <div>
        <p className="text-sm text-slate-300">
          UI/UX Designer passionate about creating immersive digital experiences. Exploring the intersection of design
          and technology.
        </p>
        <div className="flex items-center space-x-4 mt-2">
          <div className="flex items-center space-x-1">
            <Zap className="h-3 w-3 text-violet-400" />
            <span className="text-xs text-slate-400">Joined May 2024</span>
          </div>
        </div>
      </div>

      <div className="flex border-b border-slate-800">
        <button className="flex-1 text-center py-2 border-b-2 border-violet-500 text-white text-sm font-medium">
          Posts
        </button>
        <button className="flex-1 text-center py-2 text-slate-400 text-sm font-medium">Likes</button>
        <button className="flex-1 text-center py-2 text-slate-400 text-sm font-medium">Saved</button>
      </div>

      <div className="grid grid-cols-2 gap-2">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-slate-800 to-slate-900"
          >
            <img
              src={`/placeholder.svg?height=150&width=150&text=P${i}`}
              alt={`Post ${i}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>
    </div>
  )
}

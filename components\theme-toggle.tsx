"use client"

import { useState } from "react"
import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"

export default function ThemeToggle() {
  const [isDark, setIsDark] = useState(true)

  return (
    <Button
      variant="ghost"
      size="icon"
      className="rounded-full text-slate-300 hover:text-white hover:bg-slate-800 relative"
      onClick={() => setIsDark(!isDark)}
    >
      <Sun
        className={`h-[1.2rem] w-[1.2rem] transition-all ${isDark ? "scale-0 opacity-0" : "scale-100 opacity-100"}`}
      />
      <Moon
        className={`absolute h-[1.2rem] w-[1.2rem] transition-all ${isDark ? "scale-100 opacity-100" : "scale-0 opacity-0"}`}
      />
      <span className="sr-only">Toggle theme</span>

      {isDark && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          className="absolute inset-0 rounded-full bg-violet-500/10"
          layoutId="theme-indicator"
        />
      )}
    </Button>
  )
}
